using System.ComponentModel.DataAnnotations;

namespace DynamicTableAPI.Models
{
    /// <summary>
    /// Request model for creating a new dynamic table
    /// </summary>
    public class CreateTableRequest
    {
        /// <summary>
        /// Name of the table to create
        /// </summary>
        [Required(ErrorMessage = "Table name is required")]
        [RegularExpression(@"^[a-zA-Z][a-zA-Z0-9_]*$", ErrorMessage = "Table name must start with a letter and contain only letters, numbers, and underscores")]
        [StringLength(128, MinimumLength = 1, ErrorMessage = "Table name must be between 1 and 128 characters")]
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// Display label for the table (singular form)
        /// </summary>
        [StringLength(255, ErrorMessage = "Table label cannot exceed 255 characters")]
        public string? TableLabel { get; set; }

        /// <summary>
        /// Display label for the table (plural form)
        /// </summary>
        [StringLength(255, ErrorMessage = "Table label plural cannot exceed 255 characters")]
        public string? TableLabelPlural { get; set; }

        /// <summary>
        /// Whether to create file labels for this table
        /// </summary>
        public bool CreateFileLabels { get; set; } = false;

        /// <summary>
        /// Whether to create links for this table
        /// </summary>
        public bool CreateLinks { get; set; } = false;

        /// <summary>
        /// Whether to create field labels for this table
        /// </summary>
        public bool CreateFieldLabels { get; set; } = false;

        /// <summary>
        /// Whether to create form for this table
        /// </summary>
        public bool CreateForm { get; set; } = false;

        /// <summary>
        /// Whether to create desktop for this table
        /// </summary>
        public bool CreateDesktop { get; set; } = false;

        /// <summary>
        /// Whether to create permissions for this table
        /// </summary>
        public bool CreatePermissions { get; set; } = false;

        /// <summary>
        /// List of custom columns to add to the table
        /// </summary>
        [Required(ErrorMessage = "At least one column definition is required")]
        [MinLength(1, ErrorMessage = "At least one column definition is required")]
        public List<ColumnDefinition> Columns { get; set; } = new List<ColumnDefinition>();
    }
}
