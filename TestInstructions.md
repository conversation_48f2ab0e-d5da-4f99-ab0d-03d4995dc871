# Dynamic Table API - Testing Instructions

## Application Status
✅ **Application is successfully running!**

The Dynamic Table API is now running and accessible at:
- **HTTPS**: `https://localhost:7201`
- **HTTP**: `http://localhost:5021`
- **Swagger UI**: `https://localhost:7201/swagger`

## Quick Start Testing

### 1. Access Swagger UI
Open your browser and navigate to: `https://localhost:7201/swagger`

The Swagger UI provides interactive documentation where you can:
- View all available endpoints
- See request/response schemas
- Test endpoints directly in the browser
- View example requests and responses

### 2. Test Endpoints Using Swagger UI

#### Step 1: Create a Table
1. Click on **POST /api/tables/create**
2. Click "Try it out"
3. Use this sample request:

```json
{
  "tableName": "Products",
  "columns": [
    {
      "name": "Name",
      "type": "string",
      "maxLength": 100,
      "isRequired": true
    },
    {
      "name": "Price",
      "type": "decimal",
      "precision": 10,
      "scale": 2,
      "isRequired": true
    },
    {
      "name": "IsActive",
      "type": "bool",
      "defaultValue": true
    }
  ]
}
```

4. Click "Execute"
5. You should receive a **201 Created** response with the table schema

#### Step 2: List Tables
1. Click on **GET /api/tables**
2. Click "Try it out"
3. Click "Execute"
4. You should see "Products" in the response

#### Step 3: Get Table Schema
1. Click on **GET /api/tables/{tableName}**
2. Click "Try it out"
3. Enter "Products" as the tableName
4. Click "Execute"
5. You should see the complete table schema including system columns

#### Step 4: Insert Data
1. Click on **POST /api/tables/{tableName}/data**
2. Click "Try it out"
3. Enter "Products" as the tableName
4. Use this sample data:

```json
{
  "data": {
    "Name": "Laptop Computer",
    "Price": 999.99,
    "IsActive": true
  }
}
```

5. Click "Execute"
6. You should receive a **201 Created** response

#### Step 5: Retrieve Data
1. Click on **GET /api/tables/{tableName}/data**
2. Click "Try it out"
3. Enter "Products" as the tableName
4. Click "Execute"
5. You should see the inserted data with system columns (Id, CreatedDate, UpdatedDate)

## Advanced Testing Scenarios

### Test Different Data Types
Create a comprehensive table with all supported data types:

```json
{
  "tableName": "TestAllTypes",
  "columns": [
    {
      "name": "StringColumn",
      "type": "string",
      "maxLength": 50,
      "isRequired": true
    },
    {
      "name": "IntColumn",
      "type": "int",
      "isRequired": false
    },
    {
      "name": "DateTimeColumn",
      "type": "datetime",
      "isRequired": false
    },
    {
      "name": "BoolColumn",
      "type": "bool",
      "defaultValue": false
    },
    {
      "name": "DecimalColumn",
      "type": "decimal",
      "precision": 15,
      "scale": 4,
      "isRequired": true
    },
    {
      "name": "GuidColumn",
      "type": "guid",
      "isRequired": false
    }
  ]
}
```

### Test Validation Scenarios

#### Invalid Table Name
```json
{
  "tableName": "123InvalidName",
  "columns": [
    {
      "name": "TestColumn",
      "type": "string"
    }
  ]
}
```
Expected: **400 Bad Request** with validation error

#### Reserved Word Table Name
```json
{
  "tableName": "SELECT",
  "columns": [
    {
      "name": "TestColumn",
      "type": "string"
    }
  ]
}
```
Expected: **400 Bad Request** with reserved word error

#### Duplicate Table Creation
Try creating the same table twice.
Expected: **409 Conflict** on second attempt

#### Invalid Data Type
```json
{
  "tableName": "TestTable",
  "columns": [
    {
      "name": "TestColumn",
      "type": "invalidtype"
    }
  ]
}
```
Expected: **400 Bad Request** with unsupported data type error

## Testing with External Tools

### Using PowerShell (Windows)
```powershell
# Test GET endpoint
Invoke-RestMethod -Uri "https://localhost:7201/api/tables" -Method GET

# Test POST endpoint (create table)
$body = @{
    tableName = "TestTable"
    columns = @(
        @{
            name = "TestColumn"
            type = "string"
            maxLength = 100
            isRequired = $true
        }
    )
} | ConvertTo-Json -Depth 3

Invoke-RestMethod -Uri "https://localhost:7201/api/tables/create" -Method POST -Body $body -ContentType "application/json"
```

### Using curl (if available)
```bash
# Test GET endpoint
curl -k https://localhost:7201/api/tables

# Test POST endpoint
curl -k -X POST https://localhost:7201/api/tables/create \
  -H "Content-Type: application/json" \
  -d '{"tableName":"TestTable","columns":[{"name":"TestColumn","type":"string","maxLength":100,"isRequired":true}]}'
```

## Expected System Behavior

### Automatic System Columns
Every created table automatically includes:
- **Id**: Primary key, auto-increment integer
- **CreatedDate**: datetime2, defaults to GETUTCDATE()
- **UpdatedDate**: datetime2, nullable, updated on modifications

### Error Handling
The API returns appropriate HTTP status codes:
- **200 OK**: Successful retrieval
- **201 Created**: Successful creation
- **400 Bad Request**: Invalid input or validation errors
- **404 Not Found**: Table or resource not found
- **409 Conflict**: Table already exists
- **500 Internal Server Error**: Unexpected server errors

### Data Type Mapping
| API Type | SQL Server Type | Notes |
|----------|----------------|-------|
| string | nvarchar(n) | Default length: 255, max: 4000 |
| int | int | 32-bit integer |
| datetime | datetime2 | High precision date/time |
| bool | bit | True/False values |
| decimal | decimal(p,s) | Default: decimal(18,2) |
| guid | uniqueidentifier | UUID format |

## Troubleshooting

### Database Connection Issues
If you encounter database connection errors:
1. Ensure SQL Server LocalDB is installed
2. The application will continue to run but database operations may fail
3. Check the console output for detailed error messages

### Port Conflicts
If the default ports are in use:
1. Check `Properties/launchSettings.json` for port configuration
2. Modify the ports if needed
3. Update the URLs in your tests accordingly

### SSL Certificate Issues
For HTTPS testing:
1. Use the `-k` flag with curl to skip certificate validation
2. In browsers, accept the self-signed certificate warning
3. For production, configure proper SSL certificates

## Verification Checklist

✅ Application builds without errors  
✅ Application starts and listens on configured ports  
✅ Swagger UI is accessible and functional  
✅ All REST endpoints are available  
✅ Table creation works with various data types  
✅ Data insertion and retrieval work correctly  
✅ Validation errors return appropriate status codes  
✅ System columns are automatically added  
✅ Error handling middleware works correctly  
✅ API documentation is complete and accurate  

## Next Steps

1. **Production Deployment**: Configure proper connection strings and SSL certificates
2. **Authentication**: Add authentication and authorization if needed
3. **Performance**: Add caching and optimize database queries
4. **Monitoring**: Add application insights and logging
5. **Testing**: Create comprehensive unit and integration tests
6. **Security**: Implement input sanitization and SQL injection protection (already included via parameterized queries)

The Dynamic Table API is now fully functional and ready for use!
