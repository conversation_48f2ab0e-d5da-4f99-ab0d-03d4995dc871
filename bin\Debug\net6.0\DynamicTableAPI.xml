<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DynamicTableAPI</name>
    </assembly>
    <members>
        <member name="T:DynamicTableAPI.Controllers.TablesController">
            <summary>
            Controller for managing dynamic tables
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Controllers.TablesController.CreateTable(DynamicTableAPI.Models.CreateTableRequest)">
            <summary>
            Creates a new dynamic table with the specified columns
            </summary>
            <param name="request">Table creation request containing table name and column definitions</param>
            <returns>Created table schema information</returns>
            <response code="201">Table created successfully</response>
            <response code="400">Invalid request data</response>
            <response code="409">Table already exists</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:DynamicTableAPI.Controllers.TablesController.AddFileToDb(DynamicTableAPI.Models.AddFileToDbRequest)">
            <summary>
            Creates a new table with metadata using the legacy AddFileIntoDB functionality
            </summary>
            <param name="request">Table creation request with metadata</param>
            <returns>Created table information with metadata</returns>
            <response code="201">Table created successfully</response>
            <response code="400">Invalid request data or validation failed</response>
            <response code="409">Table or label already exists</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:DynamicTableAPI.Controllers.TablesController.GetTables">
            <summary>
            Gets a list of all existing tables
            </summary>
            <returns>List of table names</returns>
            <response code="200">List of tables retrieved successfully</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:DynamicTableAPI.Controllers.TablesController.GetTableSchema(System.String)">
            <summary>
            Gets schema information for a specific table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Table schema information</returns>
            <response code="200">Table schema retrieved successfully</response>
            <response code="404">Table not found</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:DynamicTableAPI.Controllers.TablesController.InsertData(System.String,DynamicTableAPI.Models.InsertDataRequest)">
            <summary>
            Inserts data into a specific table
            </summary>
            <param name="tableName">Name of the table</param>
            <param name="request">Data to insert</param>
            <returns>Number of rows affected</returns>
            <response code="201">Data inserted successfully</response>
            <response code="400">Invalid request data</response>
            <response code="404">Table not found</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="M:DynamicTableAPI.Controllers.TablesController.GetData(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Retrieves data from a specific table
            </summary>
            <param name="tableName">Name of the table</param>
            <param name="pageSize">Number of records per page (optional)</param>
            <param name="pageNumber">Page number (optional, 1-based)</param>
            <returns>List of data records</returns>
            <response code="200">Data retrieved successfully</response>
            <response code="404">Table not found</response>
            <response code="500">Internal server error</response>
        </member>
        <member name="T:DynamicTableAPI.Data.DynamicTableDbContext">
            <summary>
            Database context for dynamic table operations
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Data.DynamicTableDbContext.TableMetadata">
            <summary>
            Table metadata for storing information about dynamically created tables
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.ExecuteRawSqlAsync(System.String)">
            <summary>
            Executes raw SQL command for dynamic table creation
            </summary>
            <param name="sql">SQL command to execute</param>
            <returns>Number of rows affected</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.ExecuteRawQueryAsync(System.String)">
            <summary>
            Executes raw SQL query and returns results
            </summary>
            <param name="sql">SQL query to execute</param>
            <returns>Query results as list of dictionaries</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.GetTableSchemaAsync(System.String)">
            <summary>
            Gets table schema information
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Table schema information</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.TableExistsAsync(System.String)">
            <summary>
            Checks if a table exists in the database
            </summary>
            <param name="tableName">Name of the table to check</param>
            <returns>True if table exists, false otherwise</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.GetUserTablesAsync">
            <summary>
            Gets list of all user tables in the database
            </summary>
            <returns>List of table names</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.GetTableMetadataAsync(System.String)">
            <summary>
            Gets table metadata by table name
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Table metadata or null if not found</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.SaveTableMetadataAsync(DynamicTableAPI.Models.TableMetadata)">
            <summary>
            Saves table metadata
            </summary>
            <param name="metadata">Table metadata to save</param>
            <returns>Saved metadata</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.TableLabelExistsAsync(System.String,System.String)">
            <summary>
            Checks if a table label already exists
            </summary>
            <param name="tableLabel">Table label to check</param>
            <param name="excludeTableName">Table name to exclude from check (for updates)</param>
            <returns>True if label exists, false otherwise</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.DynamicTableDbContext.EnsureMetadataTableExistsAsync">
            <summary>
            Ensures the metadata table exists in the database
            </summary>
            <returns>True if successful</returns>
        </member>
        <member name="T:DynamicTableAPI.Data.ITableRepository">
            <summary>
            Repository interface for table operations
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.TableExistsAsync(System.String)">
            <summary>
            Checks if a table exists in the database
            </summary>
            <param name="tableName">Name of the table to check</param>
            <returns>True if table exists, false otherwise</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.GetUserTablesAsync">
            <summary>
            Gets list of all user tables in the database
            </summary>
            <returns>List of table names</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.GetTableSchemaAsync(System.String)">
            <summary>
            Gets table schema information
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Table schema information as list of dictionaries</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.ExecuteRawSqlAsync(System.String)">
            <summary>
            Executes a raw SQL command
            </summary>
            <param name="sql">SQL command to execute</param>
            <returns>Number of rows affected</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.ExecuteRawQueryAsync(System.String)">
            <summary>
            Executes a raw SQL query and returns results
            </summary>
            <param name="sql">SQL query to execute</param>
            <returns>Query results as list of dictionaries</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.GetTableRecordCountAsync(System.String)">
            <summary>
            Gets the total count of records in a table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Total number of records</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.TruncateTableAsync(System.String)">
            <summary>
            Deletes all data from a table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Number of rows deleted</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.ITableRepository.DropTableAsync(System.String)">
            <summary>
            Drops a table from the database
            </summary>
            <param name="tableName">Name of the table to drop</param>
            <returns>True if successful</returns>
        </member>
        <member name="T:DynamicTableAPI.Data.TableRepository">
            <summary>
            Repository implementation for table operations
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.TableExistsAsync(System.String)">
            <summary>
            Checks if a table exists in the database
            </summary>
            <param name="tableName">Name of the table to check</param>
            <returns>True if table exists, false otherwise</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.GetUserTablesAsync">
            <summary>
            Gets list of all user tables in the database
            </summary>
            <returns>List of table names</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.GetTableSchemaAsync(System.String)">
            <summary>
            Gets table schema information
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Table schema information as list of dictionaries</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.ExecuteRawSqlAsync(System.String)">
            <summary>
            Executes a raw SQL command
            </summary>
            <param name="sql">SQL command to execute</param>
            <returns>Number of rows affected</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.ExecuteRawQueryAsync(System.String)">
            <summary>
            Executes a raw SQL query and returns results
            </summary>
            <param name="sql">SQL query to execute</param>
            <returns>Query results as list of dictionaries</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.GetTableRecordCountAsync(System.String)">
            <summary>
            Gets the total count of records in a table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Total number of records</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.TruncateTableAsync(System.String)">
            <summary>
            Deletes all data from a table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Number of rows deleted</returns>
        </member>
        <member name="M:DynamicTableAPI.Data.TableRepository.DropTableAsync(System.String)">
            <summary>
            Drops a table from the database
            </summary>
            <param name="tableName">Name of the table to drop</param>
            <returns>True if successful</returns>
        </member>
        <member name="T:DynamicTableAPI.Middleware.GlobalExceptionMiddleware">
            <summary>
            Global exception handling middleware
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Middleware.GlobalExceptionMiddlewareExtensions">
            <summary>
            Extension method to register the global exception middleware
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Middleware.SwaggerSchemaExampleFilter">
            <summary>
            Swagger schema filter to add examples to API documentation
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.AddFileToDbRequest">
            <summary>
            Request model for adding a file/table to the database using legacy AddFileIntoDB functionality
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbRequest.TableName">
            <summary>
            Name of the table to create
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbRequest.TableLabel">
            <summary>
            Display label for the table (singular form)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbRequest.TableLabelPlural">
            <summary>
            Display label for the table (plural form)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbRequest.CreateFileLabels">
            <summary>
            Whether to create file labels for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbRequest.CreateLinks">
            <summary>
            Whether to create links for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbRequest.CreateFieldLabels">
            <summary>
            Whether to create field labels for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbRequest.CreateForm">
            <summary>
            Whether to create form for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbRequest.CreateDesktop">
            <summary>
            Whether to create desktop for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbRequest.CreatePermissions">
            <summary>
            Whether to create permissions for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbRequest.Columns">
            <summary>
            Optional list of custom columns to add to the table
            If not provided, a basic table with default columns will be created
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.AddFileToDbResponse">
            <summary>
            Response model for AddFileIntoDB operation
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbResponse.Success">
            <summary>
            Indicates whether the operation was successful
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbResponse.Message">
            <summary>
            Response message
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbResponse.TableSchema">
            <summary>
            Created table schema (if successful)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.AddFileToDbResponse.Errors">
            <summary>
            List of validation errors (if any)
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.ApiResponse`1">
            <summary>
            Generic API response wrapper
            </summary>
            <typeparam name="T">Type of the response data</typeparam>
        </member>
        <member name="P:DynamicTableAPI.Models.ApiResponse`1.Success">
            <summary>
            Indicates whether the operation was successful
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ApiResponse`1.Data">
            <summary>
            Response data
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ApiResponse`1.Message">
            <summary>
            Error message if the operation failed
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ApiResponse`1.Errors">
            <summary>
            List of validation errors
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Models.ApiResponse`1.SuccessResponse(`0,System.String)">
            <summary>
            Creates a successful response
            </summary>
            <param name="data">Response data</param>
            <param name="message">Optional success message</param>
            <returns>Successful API response</returns>
        </member>
        <member name="M:DynamicTableAPI.Models.ApiResponse`1.ErrorResponse(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            Creates an error response
            </summary>
            <param name="message">Error message</param>
            <param name="errors">List of validation errors</param>
            <returns>Error API response</returns>
        </member>
        <member name="T:DynamicTableAPI.Models.ApiResponse">
            <summary>
            Non-generic API response for operations that don't return data
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Models.ApiResponse.SuccessResponse(System.String)">
            <summary>
            Creates a successful response without data
            </summary>
            <param name="message">Success message</param>
            <returns>Successful API response</returns>
        </member>
        <member name="T:DynamicTableAPI.Models.ColumnDefinition">
            <summary>
            Represents a column definition for dynamic table creation
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.Name">
            <summary>
            Name of the column
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.Type">
            <summary>
            Data type of the column (string, int, datetime, bool, decimal, guid)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.MaxLength">
            <summary>
            Maximum length for string columns (default: 255)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.Precision">
            <summary>
            Precision for decimal columns
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.Scale">
            <summary>
            Scale for decimal columns
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.IsRequired">
            <summary>
            Whether the column is required (NOT NULL)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnDefinition.DefaultValue">
            <summary>
            Default value for the column
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.CreateTableRequest">
            <summary>
            Request model for creating a new dynamic table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.TableName">
            <summary>
            Name of the table to create
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.TableLabel">
            <summary>
            Display label for the table (singular form)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.TableLabelPlural">
            <summary>
            Display label for the table (plural form)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.CreateFileLabels">
            <summary>
            Whether to create file labels for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.CreateLinks">
            <summary>
            Whether to create links for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.CreateFieldLabels">
            <summary>
            Whether to create field labels for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.CreateForm">
            <summary>
            Whether to create form for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.CreateDesktop">
            <summary>
            Whether to create desktop for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.CreatePermissions">
            <summary>
            Whether to create permissions for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.CreateTableRequest.Columns">
            <summary>
            List of custom columns to add to the table
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.InsertDataRequest">
            <summary>
            Request model for inserting data into a dynamic table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.InsertDataRequest.Data">
            <summary>
            Data to insert as key-value pairs where key is column name and value is the data
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.BulkInsertDataRequest">
            <summary>
            Request model for inserting multiple rows of data
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.BulkInsertDataRequest.Data">
            <summary>
            List of data rows to insert
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.TableMetadata">
            <summary>
            Entity model for storing table metadata in the database
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.Id">
            <summary>
            Primary key
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.TableName">
            <summary>
            Name of the table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.TableLabel">
            <summary>
            Display label for the table (singular form)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.TableLabelPlural">
            <summary>
            Display label for the table (plural form)
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.CreateFileLabels">
            <summary>
            Whether file labels were created for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.CreateLinks">
            <summary>
            Whether links were created for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.CreateFieldLabels">
            <summary>
            Whether field labels were created for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.CreateForm">
            <summary>
            Whether form was created for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.CreateDesktop">
            <summary>
            Whether desktop was created for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.CreatePermissions">
            <summary>
            Whether permissions were created for this table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.CreatedDate">
            <summary>
            Date and time when the table was created
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableMetadata.UpdatedDate">
            <summary>
            Date and time when the table metadata was last updated
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.TableSchema">
            <summary>
            Represents the schema information of a table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableSchema.TableName">
            <summary>
            Name of the table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableSchema.Columns">
            <summary>
            List of columns in the table
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.TableSchema.CreatedDate">
            <summary>
            Date and time when the table was created
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Models.ColumnSchema">
            <summary>
            Represents the schema information of a column
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.ColumnName">
            <summary>
            Name of the column
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.DataType">
            <summary>
            Data type of the column
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.IsNullable">
            <summary>
            Whether the column allows null values
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.MaxLength">
            <summary>
            Maximum length for string columns
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.Precision">
            <summary>
            Precision for numeric columns
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.Scale">
            <summary>
            Scale for decimal columns
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.DefaultValue">
            <summary>
            Default value for the column
            </summary>
        </member>
        <member name="P:DynamicTableAPI.Models.ColumnSchema.IsPrimaryKey">
            <summary>
            Whether the column is a primary key
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Services.DynamicTableService">
            <summary>
            Service for managing dynamic table operations
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.CreateTableAsync(DynamicTableAPI.Models.CreateTableRequest)">
            <summary>
            Creates a new dynamic table with the specified columns
            </summary>
            <param name="request">Table creation request</param>
            <returns>Created table schema</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.AddFileIntoDbAsync(DynamicTableAPI.Models.AddFileToDbRequest)">
            <summary>
            Creates a new table with metadata using the legacy AddFileIntoDB functionality
            </summary>
            <param name="request">Request containing table creation parameters</param>
            <returns>Response with creation result</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.AddFileIntoDB(System.String,System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Legacy method for backward compatibility - converts to new async method
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.LoadSelltisTablesAsync(System.Boolean)">
            <summary>
            Gets list of tables with optional labels (modernized version of LoadSelltisTables)
            </summary>
            <param name="onlyTables">If true, returns only table names. If false, returns table names with labels</param>
            <returns>List of table names or table names with labels</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.LoadSelltisTables(System.Boolean)">
            <summary>
            Legacy method for backward compatibility
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GetFilesAsync(System.Boolean)">
            <summary>
            Gets list of files/tables (modernized version of GetFiles)
            </summary>
            <param name="includeSysFiles">Whether to include system files</param>
            <returns>List of table names</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GetFiles(System.Boolean)">
            <summary>
            Legacy method for backward compatibility
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.IsFileSystem(System.String)">
            <summary>
            Determines if a table name represents a system file/table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>True if it's a system table, false otherwise</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.RunSQLQueryAsync(System.String)">
            <summary>
            Executes a SQL query (modernized version of RunSQLQuery)
            </summary>
            <param name="query">SQL query to execute</param>
            <returns>True if successful, false otherwise</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.RunSQLQuery(System.String)">
            <summary>
            Legacy method for backward compatibility
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GetTableSchemaAsync(System.String)">
            <summary>
            Gets the schema information for a specific table
            </summary>
            <param name="tableName">Name of the table</param>
            <returns>Table schema information</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GetTablesAsync">
            <summary>
            Gets a list of all user tables in the database
            </summary>
            <returns>List of table names</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.InsertDataAsync(System.String,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Inserts data into a dynamic table
            </summary>
            <param name="tableName">Name of the table</param>
            <param name="data">Data to insert</param>
            <returns>Number of rows affected</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GetDataAsync(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Retrieves data from a dynamic table
            </summary>
            <param name="tableName">Name of the table</param>
            <param name="pageSize">Number of records per page (optional)</param>
            <param name="pageNumber">Page number (optional, 1-based)</param>
            <returns>List of data records</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GenerateCreateTableSql(DynamicTableAPI.Models.CreateTableRequest)">
            <summary>
            Generates CREATE TABLE SQL statement
            </summary>
            <param name="request">Table creation request</param>
            <returns>CREATE TABLE SQL statement</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GenerateColumnDefinitionSql(DynamicTableAPI.Models.ColumnDefinition)">
            <summary>
            Generates SQL column definition
            </summary>
            <param name="column">Column definition</param>
            <returns>SQL column definition</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.FormatDefaultValue(System.Object,System.String)">
            <summary>
            Formats default value for SQL
            </summary>
            <param name="value">Default value</param>
            <param name="dataType">Data type</param>
            <returns>Formatted default value</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.ConvertJsonElementToNativeType(System.Text.Json.JsonElement,System.String)">
            <summary>
            Converts JsonElement to native .NET type based on the specified data type
            </summary>
            <param name="jsonElement">JsonElement to convert</param>
            <param name="dataType">Target data type</param>
            <returns>Converted value</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.GenerateInsertSql(System.String,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            Generates INSERT SQL statement
            </summary>
            <param name="tableName">Table name</param>
            <param name="data">Data to insert</param>
            <returns>INSERT SQL statement</returns>
        </member>
        <member name="M:DynamicTableAPI.Services.DynamicTableService.FormatValueForSql(System.Object)">
            <summary>
            Formats a value for SQL insertion
            </summary>
            <param name="value">Value to format</param>
            <returns>Formatted SQL value</returns>
        </member>
        <member name="T:DynamicTableAPI.Services.IDynamicTableService">
            <summary>
            Interface for dynamic table service
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Validators.ITableValidationService">
            <summary>
            Interface for table validation service
            </summary>
        </member>
        <member name="T:DynamicTableAPI.Validators.TableValidationService">
            <summary>
            Service for validating table and column definitions
            </summary>
        </member>
        <member name="M:DynamicTableAPI.Validators.TableValidationService.ValidateCreateTableRequest(DynamicTableAPI.Models.CreateTableRequest)">
            <summary>
            Validates a table creation request
            </summary>
            <param name="request">The table creation request to validate</param>
            <returns>Validation result with any errors</returns>
        </member>
        <member name="M:DynamicTableAPI.Validators.TableValidationService.ValidateAddFileToDbRequest(DynamicTableAPI.Models.AddFileToDbRequest)">
            <summary>
            Validates an AddFileToDb request
            </summary>
            <param name="request">The AddFileToDb request to validate</param>
            <returns>Validation result with any errors</returns>
        </member>
        <member name="M:DynamicTableAPI.Validators.TableValidationService.ValidateTableName(System.String)">
            <summary>
            Validates a table name
            </summary>
            <param name="tableName">The table name to validate</param>
            <returns>Validation result</returns>
        </member>
        <member name="M:DynamicTableAPI.Validators.TableValidationService.ValidateColumnDefinition(DynamicTableAPI.Models.ColumnDefinition,System.Int32)">
            <summary>
            Validates a column definition
            </summary>
            <param name="column">The column definition to validate</param>
            <param name="index">The index of the column in the request (for error reporting)</param>
            <returns>Validation result</returns>
        </member>
        <member name="T:DynamicTableAPI.Validators.ValidationResult">
            <summary>
            Represents the result of a validation operation
            </summary>
        </member>
    </members>
</doc>
