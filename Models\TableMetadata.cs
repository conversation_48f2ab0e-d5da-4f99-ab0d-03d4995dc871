using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DynamicTableAPI.Models
{
    /// <summary>
    /// Entity model for storing table metadata in the database
    /// </summary>
    [Table("__TableMetadata")]
    public class TableMetadata
    {
        /// <summary>
        /// Primary key
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Name of the table
        /// </summary>
        [Required]
        [StringLength(128)]
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// Display label for the table (singular form)
        /// </summary>
        [StringLength(255)]
        public string? TableLabel { get; set; }

        /// <summary>
        /// Display label for the table (plural form)
        /// </summary>
        [StringLength(255)]
        public string? TableLabelPlural { get; set; }

        /// <summary>
        /// Whether file labels were created for this table
        /// </summary>
        public bool CreateFileLabels { get; set; }

        /// <summary>
        /// Whether links were created for this table
        /// </summary>
        public bool CreateLinks { get; set; }

        /// <summary>
        /// Whether field labels were created for this table
        /// </summary>
        public bool CreateFieldLabels { get; set; }

        /// <summary>
        /// Whether form was created for this table
        /// </summary>
        public bool CreateForm { get; set; }

        /// <summary>
        /// Whether desktop was created for this table
        /// </summary>
        public bool CreateDesktop { get; set; }

        /// <summary>
        /// Whether permissions were created for this table
        /// </summary>
        public bool CreatePermissions { get; set; }

        /// <summary>
        /// Date and time when the table was created
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Date and time when the table metadata was last updated
        /// </summary>
        public DateTime? UpdatedDate { get; set; }
    }
}
