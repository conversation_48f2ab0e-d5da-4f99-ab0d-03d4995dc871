using System.ComponentModel.DataAnnotations;

namespace DynamicTableAPI.Models
{
    /// <summary>
    /// Request model for adding a file/table to the database using legacy AddFileIntoDB functionality
    /// </summary>
    public class AddFileToDbRequest
    {
        /// <summary>
        /// Name of the table to create
        /// </summary>
        [Required(ErrorMessage = "Table name is required")]
        [RegularExpression(@"^[a-zA-Z][a-zA-Z0-9_]*$", ErrorMessage = "Table name must start with a letter and contain only letters, numbers, and underscores")]
        [StringLength(128, MinimumLength = 1, ErrorMessage = "Table name must be between 1 and 128 characters")]
        public string TableName { get; set; } = string.Empty;

        /// <summary>
        /// Display label for the table (singular form)
        /// </summary>
        [Required(ErrorMessage = "Table label is required")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Table label must be between 1 and 255 characters")]
        public string TableLabel { get; set; } = string.Empty;

        /// <summary>
        /// Display label for the table (plural form)
        /// </summary>
        [Required(ErrorMessage = "Table label plural is required")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Table label plural must be between 1 and 255 characters")]
        public string TableLabelPlural { get; set; } = string.Empty;

        /// <summary>
        /// Whether to create file labels for this table
        /// </summary>
        public bool CreateFileLabels { get; set; } = false;

        /// <summary>
        /// Whether to create links for this table
        /// </summary>
        public bool CreateLinks { get; set; } = false;

        /// <summary>
        /// Whether to create field labels for this table
        /// </summary>
        public bool CreateFieldLabels { get; set; } = false;

        /// <summary>
        /// Whether to create form for this table
        /// </summary>
        public bool CreateForm { get; set; } = false;

        /// <summary>
        /// Whether to create desktop for this table
        /// </summary>
        public bool CreateDesktop { get; set; } = false;

        /// <summary>
        /// Whether to create permissions for this table
        /// </summary>
        public bool CreatePermissions { get; set; } = false;

        /// <summary>
        /// Optional list of custom columns to add to the table
        /// If not provided, a basic table with default columns will be created
        /// </summary>
        public List<ColumnDefinition>? Columns { get; set; }
    }

    /// <summary>
    /// Response model for AddFileIntoDB operation
    /// </summary>
    public class AddFileToDbResponse
    {
        /// <summary>
        /// Indicates whether the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Created table schema (if successful)
        /// </summary>
        public TableSchema? TableSchema { get; set; }

        /// <summary>
        /// List of validation errors (if any)
        /// </summary>
        public List<string>? Errors { get; set; }
    }
}
