{"tableName": "Products", "columns": [{"name": "Name", "type": "string", "maxLength": 100, "isRequired": true}, {"name": "Price", "type": "decimal", "precision": 10, "scale": 2, "isRequired": true}, {"name": "IsActive", "type": "bool", "defaultValue": true}, {"name": "Description", "type": "string", "maxLength": 500, "isRequired": false}, {"name": "CategoryId", "type": "int", "isRequired": false}, {"name": "ProductId", "type": "guid", "isRequired": true}]}