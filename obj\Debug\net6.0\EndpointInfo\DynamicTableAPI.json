{"openapi": "3.0.1", "info": {"title": "Dynamic Table API", "description": "A RESTful Web API for dynamic database table creation and management", "contact": {"name": "Dynamic Table API", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/Tables/create": {"post": {"tags": ["Tables"], "summary": "Creates a new dynamic table with the specified columns", "requestBody": {"description": "Table creation request containing table name and column definitions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTableRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateTableRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateTableRequest"}}}}, "responses": {"201": {"description": "Table created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TableSchemaApiResponse"}}}}, "400": {"description": "Invalid request data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}, "409": {"description": "Table already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}}}}, "/api/Tables/add-file": {"post": {"tags": ["Tables"], "summary": "Creates a new table with metadata using the legacy AddFileIntoDB functionality", "requestBody": {"description": "Table creation request with metadata", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddFileToDbRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddFileToDbRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddFileToDbRequest"}}}}, "responses": {"201": {"description": "Table created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddFileToDbResponseApiResponse"}}}}, "400": {"description": "Invalid request data or validation failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}, "409": {"description": "Table or label already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}}}}, "/api/Tables": {"get": {"tags": ["Tables"], "summary": "Gets a list of all existing tables", "responses": {"200": {"description": "List of tables retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringListApiResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}}}}, "/api/Tables/{tableName}": {"get": {"tags": ["Tables"], "summary": "Gets schema information for a specific table", "parameters": [{"name": "tableName", "in": "path", "description": "Name of the table", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Table schema retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TableSchemaApiResponse"}}}}, "404": {"description": "Table not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}}}}, "/api/Tables/{tableName}/data": {"post": {"tags": ["Tables"], "summary": "Inserts data into a specific table", "parameters": [{"name": "tableName", "in": "path", "description": "Name of the table", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Data to insert", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertDataRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InsertDataRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InsertDataRequest"}}}}, "responses": {"201": {"description": "Data inserted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Int32ApiResponse"}}}}, "400": {"description": "Invalid request data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}, "404": {"description": "Table not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}}}, "get": {"tags": ["Tables"], "summary": "Retrieves data from a specific table", "parameters": [{"name": "tableName", "in": "path", "description": "Name of the table", "required": true, "schema": {"type": "string"}}, {"name": "pageSize", "in": "query", "description": "Number of records per page (optional)", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageNumber", "in": "query", "description": "Page number (optional, 1-based)", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Data retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringObjectDictionaryListApiResponse"}}}}, "404": {"description": "Table not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}}}}}, "components": {"schemas": {"AddFileToDbRequest": {"required": ["tableLabel", "tableLabelPlural", "tableName"], "type": "object", "properties": {"tableName": {"maxLength": 128, "minLength": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_]*$", "type": "string", "description": "Name of the table to create"}, "tableLabel": {"maxLength": 255, "minLength": 1, "type": "string", "description": "Display label for the table (singular form)"}, "tableLabelPlural": {"maxLength": 255, "minLength": 1, "type": "string", "description": "Display label for the table (plural form)"}, "createFileLabels": {"type": "boolean", "description": "Whether to create file labels for this table"}, "createLinks": {"type": "boolean", "description": "Whether to create links for this table"}, "createFieldLabels": {"type": "boolean", "description": "Whether to create field labels for this table"}, "createForm": {"type": "boolean", "description": "Whether to create form for this table"}, "createDesktop": {"type": "boolean", "description": "Whether to create desktop for this table"}, "createPermissions": {"type": "boolean", "description": "Whether to create permissions for this table"}, "columns": {"type": "array", "items": {"$ref": "#/components/schemas/ColumnDefinition"}, "description": "Optional list of custom columns to add to the table\r\nIf not provided, a basic table with default columns will be created", "nullable": true}}, "additionalProperties": false, "description": "Request model for adding a file/table to the database using legacy AddFileIntoDB functionality"}, "AddFileToDbResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the operation was successful"}, "message": {"type": "string", "description": "Response message", "nullable": true}, "tableSchema": {"$ref": "#/components/schemas/TableSchema"}, "errors": {"type": "array", "items": {"type": "string"}, "description": "List of validation errors (if any)", "nullable": true}}, "additionalProperties": false, "description": "Response model for AddFileIntoDB operation"}, "AddFileToDbResponseApiResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the operation was successful"}, "data": {"$ref": "#/components/schemas/AddFileToDbResponse"}, "message": {"type": "string", "description": "Error message if the operation failed", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "description": "List of validation errors", "nullable": true}}, "additionalProperties": false, "description": "Generic API response wrapper"}, "ColumnDefinition": {"required": ["name", "type"], "type": "object", "properties": {"name": {"minLength": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_]*$", "type": "string", "description": "Name of the column"}, "type": {"minLength": 1, "type": "string", "description": "Data type of the column (string, int, datetime, bool, decimal, guid)"}, "maxLength": {"type": "integer", "description": "Maximum length for string columns (default: 255)", "format": "int32", "nullable": true}, "precision": {"type": "integer", "description": "Precision for decimal columns", "format": "int32", "nullable": true}, "scale": {"type": "integer", "description": "Scale for decimal columns", "format": "int32", "nullable": true}, "isRequired": {"type": "boolean", "description": "Whether the column is required (NOT NULL)"}, "defaultValue": {"description": "Default value for the column", "nullable": true}}, "additionalProperties": false, "description": "Represents a column definition for dynamic table creation", "example": {"name": "SampleColumn", "type": "string", "maxLength": 255, "isRequired": false, "defaultValue": "Default Value"}}, "ColumnSchema": {"type": "object", "properties": {"columnName": {"type": "string", "description": "Name of the column", "nullable": true}, "dataType": {"type": "string", "description": "Data type of the column", "nullable": true}, "isNullable": {"type": "boolean", "description": "Whether the column allows null values"}, "maxLength": {"type": "integer", "description": "Maximum length for string columns", "format": "int32", "nullable": true}, "precision": {"type": "integer", "description": "Precision for numeric columns", "format": "int32", "nullable": true}, "scale": {"type": "integer", "description": "Scale for decimal columns", "format": "int32", "nullable": true}, "defaultValue": {"type": "string", "description": "Default value for the column", "nullable": true}, "isPrimaryKey": {"type": "boolean", "description": "Whether the column is a primary key"}}, "additionalProperties": false, "description": "Represents the schema information of a column"}, "CreateTableRequest": {"required": ["columns", "tableName"], "type": "object", "properties": {"tableName": {"maxLength": 128, "minLength": 1, "pattern": "^[a-zA-Z][a-zA-Z0-9_]*$", "type": "string", "description": "Name of the table to create"}, "tableLabel": {"maxLength": 255, "minLength": 0, "type": "string", "description": "Display label for the table (singular form)", "nullable": true}, "tableLabelPlural": {"maxLength": 255, "minLength": 0, "type": "string", "description": "Display label for the table (plural form)", "nullable": true}, "createFileLabels": {"type": "boolean", "description": "Whether to create file labels for this table"}, "createLinks": {"type": "boolean", "description": "Whether to create links for this table"}, "createFieldLabels": {"type": "boolean", "description": "Whether to create field labels for this table"}, "createForm": {"type": "boolean", "description": "Whether to create form for this table"}, "createDesktop": {"type": "boolean", "description": "Whether to create desktop for this table"}, "createPermissions": {"type": "boolean", "description": "Whether to create permissions for this table"}, "columns": {"minItems": 1, "type": "array", "items": {"$ref": "#/components/schemas/ColumnDefinition"}, "description": "List of custom columns to add to the table"}}, "additionalProperties": false, "description": "Request model for creating a new dynamic table", "example": {"tableName": "Products", "columns": [{"name": "Name", "type": "string", "maxLength": 100, "isRequired": true}, {"name": "Price", "type": "decimal", "precision": 10, "scale": 2, "isRequired": true}, {"name": "IsActive", "type": "bool", "defaultValue": true}, {"name": "Description", "type": "string", "maxLength": 500, "isRequired": false}, {"name": "CategoryId", "type": "int", "isRequired": false}, {"name": "ProductId", "type": "guid", "isRequired": true}]}}, "InsertDataRequest": {"required": ["data"], "type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"nullable": true}, "description": "Data to insert as key-value pairs where key is column name and value is the data"}}, "additionalProperties": false, "description": "Request model for inserting data into a dynamic table", "example": {"data": {"Name": "Sample Product", "Price": 29.99, "IsActive": true, "Description": "This is a sample product description", "CategoryId": 1, "ProductId": "123e4567-e89b-12d3-a456-************"}}}, "Int32ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the operation was successful"}, "data": {"type": "integer", "description": "Response data", "format": "int32"}, "message": {"type": "string", "description": "Error message if the operation failed", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "description": "List of validation errors", "nullable": true}}, "additionalProperties": false, "description": "Generic API response wrapper"}, "ObjectApiResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the operation was successful"}, "data": {"description": "Response data", "nullable": true}, "message": {"type": "string", "description": "Error message if the operation failed", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "description": "List of validation errors", "nullable": true}}, "additionalProperties": false, "description": "Generic API response wrapper"}, "StringListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the operation was successful"}, "data": {"type": "array", "items": {"type": "string"}, "description": "Response data", "nullable": true}, "message": {"type": "string", "description": "Error message if the operation failed", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "description": "List of validation errors", "nullable": true}}, "additionalProperties": false, "description": "Generic API response wrapper"}, "StringObjectDictionaryListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the operation was successful"}, "data": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "description": "Response data", "nullable": true}, "message": {"type": "string", "description": "Error message if the operation failed", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "description": "List of validation errors", "nullable": true}}, "additionalProperties": false, "description": "Generic API response wrapper"}, "TableSchema": {"type": "object", "properties": {"tableName": {"type": "string", "description": "Name of the table", "nullable": true}, "columns": {"type": "array", "items": {"$ref": "#/components/schemas/ColumnSchema"}, "description": "List of columns in the table", "nullable": true}, "createdDate": {"type": "string", "description": "Date and time when the table was created", "format": "date-time"}}, "additionalProperties": false, "description": "Represents the schema information of a table"}, "TableSchemaApiResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the operation was successful"}, "data": {"$ref": "#/components/schemas/TableSchema"}, "message": {"type": "string", "description": "Error message if the operation failed", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "description": "List of validation errors", "nullable": true}}, "additionalProperties": false, "description": "Generic API response wrapper"}}}}