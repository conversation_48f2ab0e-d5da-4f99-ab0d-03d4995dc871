﻿using DynamicTableAPI.Models;

namespace DynamicTableAPI.Validators
{
    /// <summary>
    /// Interface for table validation service
    /// </summary>
    public interface ITableValidationService
    {
        ValidationResult ValidateCreateTableRequest(CreateTableRequest request);
        ValidationResult ValidateAddFileToDbRequest(AddFileToDbRequest request);
        ValidationResult ValidateTableName(string tableName);
        ValidationResult ValidateColumnDefinition(ColumnDefinition column, int index);
    }
}
