using DynamicTableAPI.Models;
using DynamicTableAPI.Services;
using Microsoft.AspNetCore.Mvc;

namespace DynamicTableAPI.Controllers
{
    /// <summary>
    /// Controller for managing dynamic tables
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class TablesController : ControllerBase
    {
        private readonly IDynamicTableService _tableService;
        private readonly ILogger<TablesController> _logger;

        public TablesController(IDynamicTableService tableService, ILogger<TablesController> logger)
        {
            _tableService = tableService;
            _logger = logger;
        }

        /// <summary>
        /// Creates a new dynamic table with the specified columns
        /// </summary>
        /// <param name="request">Table creation request containing table name and column definitions</param>
        /// <returns>Created table schema information</returns>
        /// <response code="201">Table created successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="409">Table already exists</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("create")]
        [ProducesResponseType(typeof(ApiResponse<TableSchema>), 201)]
        [ProducesResponseType(typeof(ApiResponse<object>), 400)]
        [ProducesResponseType(typeof(ApiResponse<object>), 409)]
        [ProducesResponseType(typeof(ApiResponse<object>), 500)]
        public async Task<IActionResult> CreateTable([FromBody] CreateTableRequest request)
        {
            try
            {
                _logger.LogInformation("Received request to create table: {TableName}", request?.TableName);

                if (request == null)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse("Request body is required"));
                }

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    
                    return BadRequest(ApiResponse<object>.ErrorResponse("Validation failed", errors));
                }

                var tableSchema = await _tableService.CreateTableAsync(request);
                
                return CreatedAtAction(
                    nameof(GetTableSchema), 
                    new { tableName = request.TableName }, 
                    ApiResponse<TableSchema>.SuccessResponse(tableSchema, $"Table '{request.TableName}' created successfully"));
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid request for table creation");
                return BadRequest(ApiResponse<object>.ErrorResponse(ex.Message));
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("already exists"))
            {
                _logger.LogWarning(ex, "Attempted to create existing table");
                return Conflict(ApiResponse<object>.ErrorResponse(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error creating table: {TableName}", request?.TableName);

                // In development, provide more detailed error information
                var errorMessage = "An unexpected error occurred while creating the table";
                if (HttpContext.RequestServices.GetService<IWebHostEnvironment>()?.IsDevelopment() == true)
                {
                    errorMessage += $": {ex.Message}";
                }

                return StatusCode(500, ApiResponse<object>.ErrorResponse(errorMessage));
            }
        }

        /// <summary>
        /// Creates a new table with metadata using the legacy AddFileIntoDB functionality
        /// </summary>
        /// <param name="request">Table creation request with metadata</param>
        /// <returns>Created table information with metadata</returns>
        /// <response code="201">Table created successfully</response>
        /// <response code="400">Invalid request data or validation failed</response>
        /// <response code="409">Table or label already exists</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("add-file")]
        [ProducesResponseType(typeof(ApiResponse<AddFileToDbResponse>), 201)]
        [ProducesResponseType(typeof(ApiResponse<object>), 400)]
        [ProducesResponseType(typeof(ApiResponse<object>), 409)]
        [ProducesResponseType(typeof(ApiResponse<object>), 500)]
        public async Task<IActionResult> AddFileToDb([FromBody] AddFileToDbRequest request)
        {
            try
            {
                _logger.LogInformation("Received request to add file/table to database: {TableName}", request?.TableName);

                if (request == null)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse("Request body is required"));
                }

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();

                    return BadRequest(ApiResponse<object>.ErrorResponse("Validation failed", errors));
                }

                var result = await _tableService.AddFileIntoDbAsync(request);

                if (!result.Success)
                {
                    if (result.Message.Contains("already exists"))
                    {
                        return Conflict(ApiResponse<object>.ErrorResponse(result.Message, result.Errors));
                    }
                    else if (result.Errors != null && result.Errors.Any())
                    {
                        return BadRequest(ApiResponse<object>.ErrorResponse(result.Message, result.Errors));
                    }
                    else
                    {
                        return BadRequest(ApiResponse<object>.ErrorResponse(result.Message));
                    }
                }

                return CreatedAtAction(
                    nameof(GetTableSchema),
                    new { tableName = request.TableName },
                    ApiResponse<AddFileToDbResponse>.SuccessResponse(result, $"Table '{request.TableName}' created successfully with metadata"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error creating table with metadata: {TableName}", request?.TableName);
                return StatusCode(500, ApiResponse<object>.ErrorResponse("An unexpected error occurred while creating the table"));
            }
        }

        /// <summary>
        /// Gets a list of all existing tables
        /// </summary>
        /// <returns>List of table names</returns>
        /// <response code="200">List of tables retrieved successfully</response>
        /// <response code="500">Internal server error</response>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponse<List<string>>), 200)]
        [ProducesResponseType(typeof(ApiResponse<object>), 500)]
        public async Task<IActionResult> GetTables()
        {
            try
            {
                _logger.LogInformation("Received request to get all tables");

                var tables = await _tableService.GetTablesAsync();
                
                return Ok(ApiResponse<List<string>>.SuccessResponse(tables, $"Retrieved {tables.Count} tables"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error retrieving tables");
                return StatusCode(500, ApiResponse<object>.ErrorResponse("An unexpected error occurred while retrieving tables"));
            }
        }

        /// <summary>
        /// Gets schema information for a specific table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Table schema information</returns>
        /// <response code="200">Table schema retrieved successfully</response>
        /// <response code="404">Table not found</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("{tableName}")]
        [ProducesResponseType(typeof(ApiResponse<TableSchema>), 200)]
        [ProducesResponseType(typeof(ApiResponse<object>), 404)]
        [ProducesResponseType(typeof(ApiResponse<object>), 500)]
        public async Task<IActionResult> GetTableSchema(string tableName)
        {
            try
            {
                _logger.LogInformation("Received request to get schema for table: {TableName}", tableName);

                if (string.IsNullOrWhiteSpace(tableName))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse("Table name is required"));
                }

                var tableSchema = await _tableService.GetTableSchemaAsync(tableName);
                
                return Ok(ApiResponse<TableSchema>.SuccessResponse(tableSchema, $"Schema for table '{tableName}' retrieved successfully"));
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Table not found: {TableName}", tableName);
                return NotFound(ApiResponse<object>.ErrorResponse(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error retrieving table schema for: {TableName}", tableName);
                return StatusCode(500, ApiResponse<object>.ErrorResponse("An unexpected error occurred while retrieving table schema"));
            }
        }

        /// <summary>
        /// Inserts data into a specific table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <param name="request">Data to insert</param>
        /// <returns>Number of rows affected</returns>
        /// <response code="201">Data inserted successfully</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="404">Table not found</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("{tableName}/data")]
        [ProducesResponseType(typeof(ApiResponse<int>), 201)]
        [ProducesResponseType(typeof(ApiResponse<object>), 400)]
        [ProducesResponseType(typeof(ApiResponse<object>), 404)]
        [ProducesResponseType(typeof(ApiResponse<object>), 500)]
        public async Task<IActionResult> InsertData(string tableName, [FromBody] InsertDataRequest request)
        {
            try
            {
                _logger.LogInformation("Received request to insert data into table: {TableName}", tableName);

                if (string.IsNullOrWhiteSpace(tableName))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse("Table name is required"));
                }

                if (request == null || request.Data == null || !request.Data.Any())
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse("Data is required for insertion"));
                }

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();
                    
                    return BadRequest(ApiResponse<object>.ErrorResponse("Validation failed", errors));
                }

                var rowsAffected = await _tableService.InsertDataAsync(tableName, request.Data);
                
                return CreatedAtAction(
                    nameof(GetData), 
                    new { tableName }, 
                    ApiResponse<int>.SuccessResponse(rowsAffected, $"Successfully inserted {rowsAffected} row(s) into table '{tableName}'"));
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid request for data insertion into table: {TableName}", tableName);
                
                if (ex.Message.Contains("does not exist"))
                {
                    return NotFound(ApiResponse<object>.ErrorResponse(ex.Message));
                }
                
                return BadRequest(ApiResponse<object>.ErrorResponse(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error inserting data into table: {TableName}", tableName);
                return StatusCode(500, ApiResponse<object>.ErrorResponse("An unexpected error occurred while inserting data"));
            }
        }

        /// <summary>
        /// Retrieves data from a specific table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <param name="pageSize">Number of records per page (optional)</param>
        /// <param name="pageNumber">Page number (optional, 1-based)</param>
        /// <returns>List of data records</returns>
        /// <response code="200">Data retrieved successfully</response>
        /// <response code="404">Table not found</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("{tableName}/data")]
        [ProducesResponseType(typeof(ApiResponse<List<Dictionary<string, object?>>>), 200)]
        [ProducesResponseType(typeof(ApiResponse<object>), 404)]
        [ProducesResponseType(typeof(ApiResponse<object>), 500)]
        public async Task<IActionResult> GetData(string tableName, [FromQuery] int? pageSize = null, [FromQuery] int? pageNumber = null)
        {
            try
            {
                _logger.LogInformation("Received request to get data from table: {TableName}", tableName);

                if (string.IsNullOrWhiteSpace(tableName))
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse("Table name is required"));
                }

                // Validate pagination parameters
                if (pageSize.HasValue && pageSize.Value <= 0)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse("Page size must be greater than 0"));
                }

                if (pageNumber.HasValue && pageNumber.Value <= 0)
                {
                    return BadRequest(ApiResponse<object>.ErrorResponse("Page number must be greater than 0"));
                }

                var data = await _tableService.GetDataAsync(tableName, pageSize, pageNumber);
                
                var message = pageSize.HasValue && pageNumber.HasValue 
                    ? $"Retrieved {data.Count} records from table '{tableName}' (page {pageNumber}, size {pageSize})"
                    : $"Retrieved {data.Count} records from table '{tableName}'";
                
                return Ok(ApiResponse<List<Dictionary<string, object?>>>.SuccessResponse(data, message));
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Table not found: {TableName}", tableName);
                return NotFound(ApiResponse<object>.ErrorResponse(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error retrieving data from table: {TableName}", tableName);
                return StatusCode(500, ApiResponse<object>.ErrorResponse("An unexpected error occurred while retrieving data"));
            }
        }
    }
}
