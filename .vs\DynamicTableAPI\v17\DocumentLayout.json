{"Version": 1, "WorkspaceRootPath": "C:\\Selltis\\WebAPI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{56F74EB2-D153-2975-4DCA-437B3A75EF96}|DynamicTableAPI.csproj|c:\\selltis\\webapi\\services\\dynamictableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{56F74EB2-D153-2975-4DCA-437B3A75EF96}|DynamicTableAPI.csproj|solutionrelative:services\\dynamictableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{56F74EB2-D153-2975-4DCA-437B3A75EF96}|DynamicTableAPI.csproj|c:\\selltis\\webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{56F74EB2-D153-2975-4DCA-437B3A75EF96}|DynamicTableAPI.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:132:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "C:\\Selltis\\WebAPI\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "C:\\Selltis\\WebAPI\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-09-26T16:52:00.956Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "DynamicTableService.cs", "DocumentMoniker": "C:\\Selltis\\WebAPI\\Services\\DynamicTableService.cs", "RelativeDocumentMoniker": "Services\\DynamicTableService.cs", "ToolTip": "C:\\Selltis\\WebAPI\\Services\\DynamicTableService.cs", "RelativeToolTip": "Services\\DynamicTableService.cs", "ViewState": "AgIAABMAAAAAAAAAAAAAAB4AAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-26T16:51:12.55Z", "EditorCaption": ""}]}]}]}