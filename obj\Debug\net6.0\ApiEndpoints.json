[{"ContainingType": "DynamicTableAPI.Controllers.TablesController", "Method": "GetTables", "RelativePath": "api/Tables", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "DynamicTableAPI.Controllers.TablesController", "Method": "GetTableSchema", "RelativePath": "api/Tables/{tableName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "DynamicTableAPI.Models.ApiResponse`1[[DynamicTableAPI.Models.TableSchema, DynamicTableAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "DynamicTableAPI.Controllers.TablesController", "Method": "InsertData", "RelativePath": "api/Tables/{tableName}/data", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableName", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "DynamicTableAPI.Models.InsertDataRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Int32, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "DynamicTableAPI.Controllers.TablesController", "Method": "GetData", "RelativePath": "api/Tables/{tableName}/data", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableName", "Type": "System.String", "IsRequired": true}, {"Name": "pageSize", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Collections.Generic.List`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 404}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "DynamicTableAPI.Controllers.TablesController", "Method": "AddFileToDb", "RelativePath": "api/Tables/add-file", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DynamicTableAPI.Models.AddFileToDbRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DynamicTableAPI.Models.ApiResponse`1[[DynamicTableAPI.Models.AddFileToDbResponse, DynamicTableAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 409}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}, {"ContainingType": "DynamicTableAPI.Controllers.TablesController", "Method": "CreateTable", "RelativePath": "api/Tables/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DynamicTableAPI.Models.CreateTableRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DynamicTableAPI.Models.ApiResponse`1[[DynamicTableAPI.Models.TableSchema, DynamicTableAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 409}, {"Type": "DynamicTableAPI.Models.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 500}]}]