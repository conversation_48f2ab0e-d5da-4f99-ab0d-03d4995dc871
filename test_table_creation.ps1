$json = @"
{
  "tableName": "ProductsFinal",
  "columns": [
    {
      "name": "Name",
      "type": "string",
      "maxLength": 100,
      "isRequired": true
    },
    {
      "name": "Price",
      "type": "decimal",
      "precision": 10,
      "scale": 2,
      "isRequired": true
    },
    {
      "name": "IsActive",
      "type": "bool",
      "defaultValue": true
    },
    {
      "name": "Description",
      "type": "string",
      "maxLength": 500,
      "isRequired": false
    },
    {
      "name": "CategoryId",
      "type": "int",
      "isRequired": false
    },
    {
      "name": "ProductId",
      "type": "guid",
      "isRequired": true
    }
  ]
}
"@

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5021/api/Tables/create' -Method POST -Body $json -ContentType 'application/json'
    Write-Host "Success: $($response | ConvertTo-Json -Depth 10)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
