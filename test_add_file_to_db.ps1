# Test script for the new AddFileToDb endpoint

$baseUrl = "http://localhost:5021/api/Tables"

# Test 1: Create a table with metadata using AddFileToDb
Write-Host "Test 1: Creating table with metadata using AddFileToDb endpoint" -ForegroundColor Green

$addFileRequest = @{
    tableName = "TestCustomers"
    tableLabel = "Customer"
    tableLabelPlural = "Customers"
    createFileLabels = $true
    createLinks = $false
    createFieldLabels = $true
    createForm = $true
    createDesktop = $false
    createPermissions = $true
    columns = @(
        @{
            name = "FirstName"
            type = "string"
            maxLength = 100
            isRequired = $true
        },
        @{
            name = "LastName"
            type = "string"
            maxLength = 100
            isRequired = $true
        },
        @{
            name = "Email"
            type = "string"
            maxLength = 255
            isRequired = $true
        },
        @{
            name = "Phone"
            type = "string"
            maxLength = 20
            isRequired = $false
        },
        @{
            name = "IsActive"
            type = "bool"
            defaultValue = $true
        }
    )
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/add-file" -Method POST -Body $addFileRequest -ContentType 'application/json'
    Write-Host "Success: Table created with metadata" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 10) -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Yellow
    }
}

Write-Host "`n" + "="*50 + "`n"

# Test 2: Try to create a table with duplicate name (should fail)
Write-Host "Test 2: Attempting to create table with duplicate name (should fail)" -ForegroundColor Green

$duplicateRequest = @{
    tableName = "TestCustomers"
    tableLabel = "Customer Duplicate"
    tableLabelPlural = "Customers Duplicate"
    createFileLabels = $false
    createLinks = $false
    createFieldLabels = $false
    createForm = $false
    createDesktop = $false
    createPermissions = $false
    columns = @(
        @{
            name = "Name"
            type = "string"
            maxLength = 100
            isRequired = $true
        }
    )
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/add-file" -Method POST -Body $duplicateRequest -ContentType 'application/json'
    Write-Host "Unexpected Success: $($response | ConvertTo-Json -Depth 10)" -ForegroundColor Yellow
} catch {
    Write-Host "Expected Error: $($_.Exception.Message)" -ForegroundColor Green
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Cyan
    }
}

Write-Host "`n" + "="*50 + "`n"

# Test 3: Try to create a table with duplicate label (should fail)
Write-Host "Test 3: Attempting to create table with duplicate label (should fail)" -ForegroundColor Green

$duplicateLabelRequest = @{
    tableName = "TestCustomers2"
    tableLabel = "Customer"
    tableLabelPlural = "Customers Different"
    createFileLabels = $false
    createLinks = $false
    createFieldLabels = $false
    createForm = $false
    createDesktop = $false
    createPermissions = $false
    columns = @(
        @{
            name = "Name"
            type = "string"
            maxLength = 100
            isRequired = $true
        }
    )
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/add-file" -Method POST -Body $duplicateLabelRequest -ContentType 'application/json'
    Write-Host "Unexpected Success: $($response | ConvertTo-Json -Depth 10)" -ForegroundColor Yellow
} catch {
    Write-Host "Expected Error: $($_.Exception.Message)" -ForegroundColor Green
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Cyan
    }
}

Write-Host "`n" + "="*50 + "`n"

# Test 4: Get table schema to verify it was created correctly
Write-Host "Test 4: Getting table schema to verify creation" -ForegroundColor Green

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/TestCustomers" -Method GET
    Write-Host "Success: Retrieved table schema" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 10) -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Yellow
    }
}

Write-Host "`n" + "="*50 + "`n"

# Test 5: Insert data into the created table
Write-Host "Test 5: Inserting data into the created table" -ForegroundColor Green

$insertRequest = @{
    data = @{
        FirstName = "John"
        LastName = "Doe"
        Email = "<EMAIL>"
        Phone = "555-1234"
        IsActive = $true
    }
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/TestCustomers/data" -Method POST -Body $insertRequest -ContentType 'application/json'
    Write-Host "Success: Data inserted" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 10) -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Yellow
    }
}

Write-Host "`nTest completed!" -ForegroundColor Green
