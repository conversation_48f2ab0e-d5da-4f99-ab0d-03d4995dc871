using Microsoft.EntityFrameworkCore;
using DynamicTableAPI.Models;

namespace DynamicTableAPI.Data
{
    /// <summary>
    /// Database context for dynamic table operations
    /// </summary>
    public class DynamicTableDbContext : DbContext
    {
        public DynamicTableDbContext(DbContextOptions<DynamicTableDbContext> options) : base(options)
        {
        }

        /// <summary>
        /// Table metadata for storing information about dynamically created tables
        /// </summary>
        public DbSet<TableMetadata> TableMetadata { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure TableMetadata entity
            modelBuilder.Entity<TableMetadata>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TableName).IsRequired().HasMaxLength(128);
                entity.Property(e => e.TableLabel).HasMaxLength(255);
                entity.Property(e => e.TableLabelPlural).HasMaxLength(255);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");

                // Create unique index on TableName
                entity.HasIndex(e => e.TableName).IsUnique();
            });
        }

        /// <summary>
        /// Executes raw SQL command for dynamic table creation
        /// </summary>
        /// <param name="sql">SQL command to execute</param>
        /// <returns>Number of rows affected</returns>
        public async Task<int> ExecuteRawSqlAsync(string sql)
        {
            return await Database.ExecuteSqlRawAsync(sql);
        }

        /// <summary>
        /// Executes raw SQL query and returns results
        /// </summary>
        /// <param name="sql">SQL query to execute</param>
        /// <returns>Query results as list of dictionaries</returns>
        public async Task<List<Dictionary<string, object?>>> ExecuteRawQueryAsync(string sql)
        {
            var results = new List<Dictionary<string, object?>>();
            
            using var command = Database.GetDbConnection().CreateCommand();
            command.CommandText = sql;
            
            await Database.OpenConnectionAsync();
            
            try
            {
                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    var row = new Dictionary<string, object?>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        row[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    }
                    results.Add(row);
                }
            }
            finally
            {
                await Database.CloseConnectionAsync();
            }
            
            return results;
        }

        /// <summary>
        /// Gets table schema information
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Table schema information</returns>
        public async Task<List<Dictionary<string, object?>>> GetTableSchemaAsync(string tableName)
        {
            var sql = @"
                SELECT
                    c.COLUMN_NAME as ColumnName,
                    c.DATA_TYPE as DataType,
                    c.IS_NULLABLE as IsNullable,
                    c.CHARACTER_MAXIMUM_LENGTH as MaxLength,
                    c.NUMERIC_PRECISION as Precision,
                    c.NUMERIC_SCALE as Scale,
                    c.COLUMN_DEFAULT as DefaultValue,
                    CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IsPrimaryKey
                FROM INFORMATION_SCHEMA.COLUMNS c
                LEFT JOIN (
                    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
                    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
                        ON tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
                        AND tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
                ) pk ON c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
                WHERE c.TABLE_NAME = {0}
                ORDER BY c.ORDINAL_POSITION";

            return await ExecuteRawQueryAsync(sql.Replace("{0}", $"'{tableName}'"));
        }

        /// <summary>
        /// Checks if a table exists in the database
        /// </summary>
        /// <param name="tableName">Name of the table to check</param>
        /// <returns>True if table exists, false otherwise</returns>
        public async Task<bool> TableExistsAsync(string tableName)
        {
            var sql = @"
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = {0} AND TABLE_TYPE = 'BASE TABLE'";

            var result = await ExecuteRawQueryAsync(sql.Replace("{0}", $"'{tableName}'"));
            return Convert.ToInt32(result.First().Values.First()) > 0;
        }

        /// <summary>
        /// Gets list of all user tables in the database
        /// </summary>
        /// <returns>List of table names</returns>
        public async Task<List<string>> GetUserTablesAsync()
        {
            var sql = @"
                SELECT TABLE_NAME
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_SCHEMA = 'dbo'
                AND TABLE_NAME NOT LIKE '__TableMetadata'
                ORDER BY TABLE_NAME";

            var result = await ExecuteRawQueryAsync(sql);
            return result.Select(r => r["TABLE_NAME"]?.ToString() ?? "").Where(t => !string.IsNullOrEmpty(t)).ToList();
        }

        /// <summary>
        /// Gets table metadata by table name
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Table metadata or null if not found</returns>
        public async Task<TableMetadata?> GetTableMetadataAsync(string tableName)
        {
            return await TableMetadata.FirstOrDefaultAsync(tm => tm.TableName == tableName);
        }

        /// <summary>
        /// Saves table metadata
        /// </summary>
        /// <param name="metadata">Table metadata to save</param>
        /// <returns>Saved metadata</returns>
        public async Task<TableMetadata> SaveTableMetadataAsync(TableMetadata metadata)
        {
            var existing = await GetTableMetadataAsync(metadata.TableName);
            if (existing != null)
            {
                // Update existing metadata
                existing.TableLabel = metadata.TableLabel;
                existing.TableLabelPlural = metadata.TableLabelPlural;
                existing.CreateFileLabels = metadata.CreateFileLabels;
                existing.CreateLinks = metadata.CreateLinks;
                existing.CreateFieldLabels = metadata.CreateFieldLabels;
                existing.CreateForm = metadata.CreateForm;
                existing.CreateDesktop = metadata.CreateDesktop;
                existing.CreatePermissions = metadata.CreatePermissions;
                existing.UpdatedDate = DateTime.UtcNow;

                TableMetadata.Update(existing);
                await SaveChangesAsync();
                return existing;
            }
            else
            {
                // Add new metadata
                TableMetadata.Add(metadata);
                await SaveChangesAsync();
                return metadata;
            }
        }

        /// <summary>
        /// Checks if a table label already exists
        /// </summary>
        /// <param name="tableLabel">Table label to check</param>
        /// <param name="excludeTableName">Table name to exclude from check (for updates)</param>
        /// <returns>True if label exists, false otherwise</returns>
        public async Task<bool> TableLabelExistsAsync(string tableLabel, string? excludeTableName = null)
        {
            var query = TableMetadata.Where(tm =>
                (tm.TableLabel != null && tm.TableLabel.ToUpper() == tableLabel.ToUpper()) ||
                (tm.TableLabelPlural != null && tm.TableLabelPlural.ToUpper() == tableLabel.ToUpper()));

            if (!string.IsNullOrEmpty(excludeTableName))
            {
                query = query.Where(tm => tm.TableName != excludeTableName);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// Ensures the metadata table exists in the database
        /// </summary>
        /// <returns>True if successful</returns>
        public async Task<bool> EnsureMetadataTableExistsAsync()
        {
            try
            {
                // Check if the metadata table exists
                if (!await TableExistsAsync("__TableMetadata"))
                {
                    // Create the metadata table
                    var createTableSql = @"
                        CREATE TABLE [__TableMetadata] (
                            [Id] int IDENTITY(1,1) PRIMARY KEY,
                            [TableName] nvarchar(128) NOT NULL UNIQUE,
                            [TableLabel] nvarchar(255) NULL,
                            [TableLabelPlural] nvarchar(255) NULL,
                            [CreateFileLabels] bit NOT NULL DEFAULT 0,
                            [CreateLinks] bit NOT NULL DEFAULT 0,
                            [CreateFieldLabels] bit NOT NULL DEFAULT 0,
                            [CreateForm] bit NOT NULL DEFAULT 0,
                            [CreateDesktop] bit NOT NULL DEFAULT 0,
                            [CreatePermissions] bit NOT NULL DEFAULT 0,
                            [CreatedDate] datetime2 NOT NULL DEFAULT GETUTCDATE(),
                            [UpdatedDate] datetime2 NULL
                        )";

                    await ExecuteRawSqlAsync(createTableSql);
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
