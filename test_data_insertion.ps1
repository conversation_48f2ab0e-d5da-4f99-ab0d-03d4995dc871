$json = @"
{
  "data": {
    "Name": "Test Product",
    "Price": 99.99,
    "IsActive": true,
    "Description": "This is a test product",
    "CategoryId": 1,
    "ProductId": "12345678-1234-1234-1234-123456789012"
  }
}
"@

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5021/api/Tables/ProductsFinal/data' -Method POST -Body $json -ContentType 'application/json'
    Write-Host "Success: $($response | ConvertTo-Json -Depth 10)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
